# mcp_config.py

MCP_SERVER_CONFIG = {
    # "AaveMCPServer": {
    #     # make sure you start your weather server on port 8000
    #     "url": "http://localhost:8000/mcp",
    #     "transport": "streamable_http",
    # },
    # "AaveMCPServer": {
    #     "command": "python",
    #     # Replace with absolute path to your mcp_server.py file
    #     "args": ["./protocol/aave_mcp_server.py"],
    #     "transport": "stdio",
    # }
    "mariadb-mcp-server": {
        "url": "http://127.0.0.1:9001/sse",
        "transport": "sse"
    }
}