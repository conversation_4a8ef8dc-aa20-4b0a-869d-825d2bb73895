# JSONAPIParser 使用示例

## 完整使用示例

以下是一个完整的使用示例，展示如何配置和使用新的JSONAPIParser：

### 1. 配置示例

假设我们要爬取一个电商网站的商品API，返回的JSON格式如下：

```json
{
  "status": "success",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "iPhone 15",
        "price": 5999,
        "category": "手机",
        "brand": "Apple"
      },
      {
        "id": 2,
        "name": "MacBook Pro",
        "price": 12999,
        "category": "电脑",
        "brand": "Apple"
      }
    ],
    "pagination": {
      "page": 1,
      "total": 100
    }
  }
}
```

### 2. 解析配置

```json
{
  "product_list": [
    {
      "parse_mode": "raw",
      "store_engine": "mongodb",
      "collection": "raw_product_pages"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "products",
      "jsonpath": "$.data.products[*]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mysql",
      "tablename": "product_stats",
      "jsonpath": "$.data.pagination"
    }
  ]
}
```

### 3. 处理结果

使用上述配置，JSONAPIParser会：

1. **Raw模式 + MongoDB**: 将完整的JSON响应保存到`raw_product_pages`集合
   ```json
   {
     "data": "{\"status\":\"success\",\"data\":{...}}",
     "task_id": "task-123",
     "create_time": "2024-01-01T12:00:00Z"
   }
   ```

2. **JSONPath模式 + MongoDB**: 提取商品数组并保存到`products`集合
   ```json
   // 第一条记录
   {
     "id": 1,
     "name": "iPhone 15",
     "price": 5999,
     "category": "手机",
     "brand": "Apple"
   }
   // 第二条记录
   {
     "id": 2,
     "name": "MacBook Pro",
     "price": 12999,
     "category": "电脑",
     "brand": "Apple"
   }
   ```

3. **JSONPath模式 + MySQL**: 提取分页信息并保存到`product_stats`表

   需要预先创建表：
   ```sql
   CREATE TABLE product_stats (
     id INT AUTO_INCREMENT PRIMARY KEY,
     page INT,
     total INT
   );
   ```

   插入的数据：
   ```sql
   INSERT INTO product_stats (page, total) VALUES (1, 100);
   ```

### 4. 高级JSONPath示例

```json
{
  "advanced_parsing": [
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "apple_products",
      "jsonpath": "$.data.products[?(@.brand == 'Apple')]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "expensive_products",
      "jsonpath": "$.data.products[?(@.price > 10000)]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "product_names",
      "jsonpath": "$.data.products[*].name"
    }
  ]
}
```

### 5. 错误处理示例

```json
{
  "invalid_config": [
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      // 错误：缺少collection配置
      "jsonpath": "$.data.products[*]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mysql",
      // 错误：缺少tablename配置
      "jsonpath": "$.data.products[*]"
    },
    {
      "parse_mode": "jsonpath",
      "store_engine": "mongodb",
      "collection": "products"
      // 错误：缺少jsonpath配置
    }
  ]
}
```

### 6. MySQL表结构示例

对于上述配置，需要预先创建以下MySQL表：

```sql
-- 商品表（对应products集合的JSONPath提取）
CREATE TABLE products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  product_id INT,           -- 对应JSON中的id字段
  name VARCHAR(255),        -- 对应JSON中的name字段
  price DECIMAL(10,2),      -- 对应JSON中的price字段
  category VARCHAR(100),    -- 对应JSON中的category字段
  brand VARCHAR(100),       -- 对应JSON中的brand字段
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 分页统计表
CREATE TABLE product_stats (
  id INT AUTO_INCREMENT PRIMARY KEY,
  page INT,
  total INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Raw数据表
CREATE TABLE raw_product_pages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  data TEXT,
  task_id VARCHAR(255),
  create_time TIMESTAMP
);
```

### 7. 环境变量配置

确保设置以下环境变量：

```bash
# MongoDB配置
export MONGO_URI="mongodb://localhost:27017"
export MONGO_DATABASE="crawler_db"
export MONGO_COLLECTION="default_collection"

# MySQL配置
export MYSQL_DSN="user:password@tcp(localhost:3306)/crawler_db"
export MYSQL_TABLE="default_table"
```

### 7. 代码使用示例

```go
// 创建解析器
parser, err := parser.NewJSONAPIParser()
if err != nil {
    log.Fatal(err)
}
defer parser.Close(context.Background())

// 准备数据
content := []byte(`{"status":"success","data":{"products":[...]}}`)
urlItem := &models.URLQueueItem{
    TaskID:   "task-123",
    PageType: "product_list",
    URL:      "https://api.example.com/products",
}
parseConfig := `{"product_list":[...]}`

// 执行解析
result, err := parser.Parse(context.Background(), content, urlItem, parseConfig)
if err != nil {
    log.Printf("解析失败: %v", err)
    return
}

log.Printf("解析完成，处理了 %d 个URL项", len(result.Items))
```

## 列检查功能示例

新的列检查功能可以自动处理表结构不完整的情况：

### 示例场景

假设你有以下JSON数据：
```json
{
  "id": 123,
  "name": "商品名称",
  "price": 99.99,
  "category": "电子产品",
  "description": "详细描述",
  "extra_field": "额外字段"
}
```

但是MySQL表只有部分列：
```sql
CREATE TABLE products (
  id INT PRIMARY KEY,
  name VARCHAR(255),
  price DECIMAL(10,2)
  -- 注意：没有 category, description, extra_field 列
);
```

### 处理结果

使用新的列检查功能：
- ✅ `id`, `name`, `price` 字段会正常插入
- ✅ `category`, `description`, `extra_field` 字段会被自动跳过
- ✅ 插入操作成功完成，不会因为缺少列而失败
- ✅ 表列信息会被缓存，后续插入更快

### 日志输出示例

```
INFO: 表 products 中找到 3 个匹配的列: [id, name, price]
INFO: 跳过不存在的列: [category, description, extra_field]
INFO: 成功插入数据到表 products
```

## 注意事项

1. **数据库连接**: 确保MongoDB和MySQL连接配置正确

2. **MySQL表结构要求**:
   - **Raw模式**: 表必须有 `data`、`task_id`、`create_time` 三列
   - **JSONPath模式**: 表的列名建议与JSON对象的字段名对应
   - 字段类型要兼容JSON值类型（如INT、VARCHAR、DECIMAL等）

3. **字段映射规则（已优化）**:
   - JSON字段名 → MySQL列名（一一对应）
   - JSON字段值 → MySQL列值
   - **自动列检查**: 插入前会自动检查表中是否存在对应的列
   - **智能跳过**: 不存在的列会被自动跳过，不会导致插入失败
   - **缓存机制**: 表列信息会被缓存，提高查询性能

4. **JSONPath语法**: 使用oliveagle/jsonpath库的完整语法

5. **错误处理**:
   - 配置错误会导致解析失败
   - **改进**: MySQL列不存在不再导致插入失败，会自动跳过
   - 如果表中没有任何匹配的列，会返回错误
   - 需要检查日志排查问题

6. **性能考虑**:
   - 大量数据时考虑批量插入的性能影响
   - 表列信息缓存减少了数据库查询次数
