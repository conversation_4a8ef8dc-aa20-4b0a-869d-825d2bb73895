# 列检查功能使用示例

## 功能概述

新的列检查功能可以在插入数据到MySQL表之前，自动检查表中是否存在对应的列。如果列不存在，该字段会被自动跳过，避免插入失败。

## 主要特性

1. **自动列检查**: 插入前自动查询表结构
2. **智能跳过**: 不存在的列会被跳过，不影响插入
3. **缓存优化**: 表列信息会被缓存，提高性能
4. **错误处理**: 如果没有任何匹配的列，会返回明确的错误信息

## 使用场景

### 场景1: 表结构不完整

假设你有以下JSON数据：
```json
{
  "id": 123,
  "name": "iPhone 15",
  "price": 5999.00,
  "category": "手机",
  "brand": "Apple",
  "description": "最新款iPhone",
  "stock": 100,
  "created_at": "2024-01-01T10:00:00Z"
}
```

但MySQL表只有部分列：
```sql
CREATE TABLE products (
  id INT PRIMARY KEY,
  name VARCHAR(255),
  price DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- 注意：缺少 category, brand, description, stock 列
```

### 处理结果

使用新的列检查功能：
- ✅ `id`, `name`, `price`, `created_at` 字段正常插入
- ✅ `category`, `brand`, `description`, `stock` 字段自动跳过
- ✅ 插入成功，不会因为缺少列而失败

实际执行的SQL：
```sql
INSERT INTO products (id, name, price, created_at) VALUES (123, 'iPhone 15', 5999.00, '2024-01-01T10:00:00Z');
```

### 场景2: 动态字段处理

当JSON数据包含动态字段时，列检查功能特别有用：

```json
{
  "id": 456,
  "name": "MacBook Pro",
  "price": 12999.00,
  "specs_cpu": "M3 Pro",
  "specs_memory": "16GB",
  "specs_storage": "512GB",
  "extra_warranty": "AppleCare+",
  "promotion_code": "SAVE100"
}
```

如果表结构为：
```sql
CREATE TABLE products (
  id INT PRIMARY KEY,
  name VARCHAR(255),
  price DECIMAL(10,2),
  specs_cpu VARCHAR(100),
  specs_memory VARCHAR(50),
  specs_storage VARCHAR(50)
);
-- 注意：没有 extra_warranty, promotion_code 列
```

结果：
- ✅ 基本字段和规格字段正常插入
- ✅ 额外字段自动跳过
- ✅ 无需修改代码或配置

## 配置示例

JSONPath模式配置：
```json
{
  "product_detail": [
    {
      "parse_mode": "jsonpath",
      "store_engine": "mysql",
      "tablename": "products",
      "jsonpath": "$"
    }
  ]
}
```

## 性能优化

1. **缓存机制**: 表列信息会被缓存，避免重复查询
2. **批量处理**: 同一表的多次插入只需查询一次列信息
3. **内存效率**: 缓存使用map结构，查询效率高

## 错误处理

### 情况1: 部分列匹配
- 有匹配的列：正常插入匹配的字段
- 无匹配的列：自动跳过，记录日志

### 情况2: 完全不匹配
- 返回错误：`表 products 中没有找到任何匹配的列`
- 不会执行插入操作

### 情况3: 数据库连接问题
- 返回错误：`获取表 products 的列信息失败: [具体错误]`
- 提供详细的错误信息便于调试

## 最佳实践

1. **表设计**: 建议表包含常用的基础字段
2. **字段命名**: JSON字段名与数据库列名保持一致
3. **数据类型**: 确保JSON值类型与数据库列类型兼容
4. **监控**: 关注跳过的字段，及时调整表结构

## 兼容性

- ✅ 向后兼容：现有代码无需修改
- ✅ 渐进式：可以逐步完善表结构
- ✅ 灵活性：支持动态字段和表结构变更

## 总结

列检查功能大大提高了JSON API解析器的健壮性和灵活性，让你可以：

1. 无需担心表结构不完整导致的插入失败
2. 灵活处理动态JSON字段
3. 渐进式完善数据库表结构
4. 提高系统的容错能力

这个功能特别适合处理第三方API数据，因为API返回的字段可能会变化，而你的数据库表结构可能无法及时跟上。
