package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"shovel/spider/internal/models"
	"time"

	"github.com/dapr/go-sdk/client"
)

// URLSchedulerService URL调度服务
type URLSchedulerService struct {
	daprClient       client.Client
	taskManagerAppID string
	pubsubName       string
	topicName        string
}

// NewURLSchedulerService 创建URL调度服务
func NewURLSchedulerService(taskManagerAppID, pubsubName, topicName string) (*URLSchedulerService, error) {
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	return &URLSchedulerService{
		daprClient:       daprClient,
		taskManagerAppID: taskManagerAppID,
		pubsubName:       pubsubName,
		topicName:        topicName,
	}, nil
}

// Start 启动URL调度服务
func (s *URLSchedulerService) Start(ctx context.Context) error {
	log.Println("启动URL调度服务...")

	// 启动定期获取URL并发布到Pubsub的goroutine
	go s.scheduleURLsLoop(ctx)

	// 启动周期性任务检查的goroutine
	go s.checkRecurringTasksLoop(ctx)

	// 启动任务完成状态检查的goroutine
	go s.checkTaskCompletionLoop(ctx)

	log.Println("URL调度服务已启动")
	return nil
}

// scheduleURLsLoop 定期获取URL并发布到Pubsub
func (s *URLSchedulerService) scheduleURLsLoop(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			s.scheduleURLs(ctx)
		}
	}
}

// checkRecurringTasksLoop 定期检查周期性任务并重新执行
func (s *URLSchedulerService) checkRecurringTasksLoop(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			s.checkRecurringTasks(ctx)
		}
	}
}

// checkTaskCompletionLoop 定期检查任务完成状态
func (s *URLSchedulerService) checkTaskCompletionLoop(ctx context.Context) {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			s.checkTaskCompletion(ctx)
		}
	}
}

// checkTaskCompletion 检查任务完成状态
func (s *URLSchedulerService) checkTaskCompletion(ctx context.Context) {
	// 获取所有运行中的任务
	runningTasks, err := s.getTasksByStatus(ctx, models.StatusRunning)
	if err != nil {
		log.Printf("获取运行中任务失败: %v", err)
		return
	}

	for _, task := range runningTasks {
		// 获取任务URL统计
		stats, err := s.getTaskURLStats(ctx, task.ID)
		if err != nil {
			log.Printf("获取任务 %s 的URL统计失败: %v", task.ID, err)
			continue
		}

		// 检查是否所有URL都已处理完成
		pendingCount := stats[models.URLStatusPending]
		scheduledCount := stats[models.URLStatusScheduled]
		processingCount := stats[models.URLStatusProcessing]
		completedCount := stats[models.URLStatusCompleted]
		failedCount := stats[models.URLStatusFailed]

		// 如果没有待处理、已调度或处理中的URL，且有已完成或失败的URL
		if (pendingCount+scheduledCount+processingCount == 0) &&
			(completedCount+failedCount > 0) {

			// 根据失败率决定任务状态
			//totalProcessed := completedCount + failedCount
			//failureRate := float64(failedCount) / float64(totalProcessed)

			var newStatus models.TaskStatus
			//if failureRate > 0.5 { // 如果失败率超过50%，标记为失败
			// 	newStatus = models.StatusFailed
			// } else {
			if completedCount > 0 {
				newStatus = models.StatusCompleted
			} else {
				newStatus = models.StatusFailed
			}
			// }

			// 更新任务状态
			err = s.updateTaskStatus(ctx, task.ID, newStatus)
			if err != nil {
				log.Printf("更新任务 %s 状态失败: %v", task.ID, err)
			} else {
				log.Printf("任务 %s 已完成，状态更新为 %s (完成: %d, 失败: %d)",
					task.ID, newStatus, completedCount, failedCount)
			}
		}
	}
}

// getTasksByStatus 获取指定状态的任务
func (s *URLSchedulerService) getTasksByStatus(ctx context.Context, status models.TaskStatus) ([]*models.CrawlerTask, error) {
	// 通过调用task-manager的API接口获取任务列表，使用状态过滤功能
	const pageSize = 100 // 每页获取的任务数量
	offset := 0
	runningTasks := make([]*models.CrawlerTask, 0)

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	for {
		url := fmt.Sprintf("http://localhost:3500/api/v1/tasks?limit=%d&offset=%d&status=%s", pageSize, offset, status)
		req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
		if err != nil {
			return nil, fmt.Errorf("创建请求失败: %w", err)
		}
		req.Header.Set("dapr-app-id", s.taskManagerAppID)

		resp, err := httpClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("调用taskmanager服务获取任务列表失败: %w", err)
		}

		// 检查响应状态
		if resp.StatusCode >= 400 {
			respBody, _ := io.ReadAll(resp.Body)
			resp.Body.Close()
			return nil, fmt.Errorf("调用taskmanager服务获取任务列表失败, 状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
		}

		respBody, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			return nil, fmt.Errorf("读取响应失败: %w", err)
		}

		var response struct {
			Success bool   `json:"success"`
			Error   string `json:"error"`
			Data    struct {
				Tasks []models.CrawlerTask `json:"tasks"`
				Total int                  `json:"total"`
			} `json:"data"`
		}

		if err := json.Unmarshal(respBody, &response); err != nil {
			return nil, fmt.Errorf("解析响应数据失败: %w", err)
		}

		if !response.Success {
			return nil, fmt.Errorf("获取任务列表失败: %s", response.Error)
		}

		// 添加任务到结果列表
		for i := range response.Data.Tasks {
			runningTasks = append(runningTasks, &response.Data.Tasks[i])
		}

		// 判断是否已获取所有任务
		if len(response.Data.Tasks) < pageSize || offset+len(response.Data.Tasks) >= response.Data.Total {
			break
		}

		// 更新偏移量，获取下一页
		offset += len(response.Data.Tasks)

		// 防止过度请求
		if offset > 1000 { // 设置一个合理的上限，避免无限循环
			log.Printf("警告: 任务数量超过1000，仅获取前1000个任务")
			break
		}
	}

	return runningTasks, nil
}

// getTaskURLStats 获取任务的URL统计
func (s *URLSchedulerService) getTaskURLStats(ctx context.Context, taskID string) (map[models.URLStatus]int, error) {
	url := fmt.Sprintf("http://localhost:3500/api/v1/tasks/%s/urls/stats", taskID)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("dapr-app-id", s.taskManagerAppID)

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("调用taskmanager服务获取URL统计失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode >= 400 {
		respBody, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("调用taskmanager服务获取URL统计失败, 状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var response struct {
		Success bool `json:"success"`
		Data    struct {
			Stats map[string]int `json:"stats"`
		} `json:"data"`
		Error string `json:"error"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("获取URL统计失败: %s", response.Error)
	}

	// 将字符串状态转换为URLStatus类型
	stats := make(map[models.URLStatus]int)
	for status, count := range response.Data.Stats {
		stats[models.URLStatus(status)] = count
	}

	return stats, nil
}

// updateTaskStatus 更新任务状态
func (s *URLSchedulerService) updateTaskStatus(ctx context.Context, taskID string, status models.TaskStatus) error {
	// 构建请求体
	payload := map[string]interface{}{
		"status": status,
	}

	data, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	url := fmt.Sprintf("http://localhost:3500/api/v1/tasks/%s/status", taskID)
	req, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("dapr-app-id", s.taskManagerAppID)

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("调用taskmanager服务更新任务状态失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode >= 400 {
		respBody, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("调用taskmanager服务更新任务状态失败, 状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	return nil
}

// checkRecurringTasks 检查周期性任务并重新执行
func (s *URLSchedulerService) checkRecurringTasks(ctx context.Context) {
	log.Println("检查周期性任务...")

	// 获取所有任务ID列表
	taskIDs, err := s.getAllTaskIDs(ctx)
	if err != nil {
		log.Printf("获取任务ID列表失败: %v", err)
		return
	}

	now := time.Now()

	// 检查每个任务
	for _, taskID := range taskIDs {
		task, err := s.getTask(ctx, taskID)
		if err != nil {
			log.Printf("获取任务 %s 失败: %v", taskID, err)
			continue
		}

		// 检查是否为周期性任务
		if !task.IsRecurring || task.RepeatInterval <= 0 || task.LastExecutedAt == nil {
			continue
		}

		// 检查任务状态
		if task.Status != models.StatusCompleted &&
			task.Status != models.StatusFailed &&
			task.Status != models.StatusRunning {
			continue
		}

		// 计算下次执行时间
		nextExecutionTime := task.LastExecutedAt.Add(time.Duration(task.RepeatInterval) * time.Second)

		// 如果到达或超过下次执行时间，重新执行任务
		if now.After(nextExecutionTime) || now.Equal(nextExecutionTime) {
			log.Printf("重新执行周期性任务: %s", taskID)
			// 如果任务已经是运行中状态，则跳过执行，防止同时执行
			if task.Status == models.StatusRunning {
				log.Printf("警告: 任务 %s 当前状态为运行中，跳过本周期的执行", taskID)
				continue
			}

			// 先将任务状态更新为运行中
			err = s.updateTaskStatus(ctx, taskID, models.StatusRunning)
			if err != nil {
				log.Printf("更新任务 %s 状态为运行中失败: %v", taskID, err)
				continue
			}

			err = s.reExecuteTask(ctx, task)
			if err != nil {
				log.Printf("重新执行任务 %s 失败: %v", taskID, err)
			}
		}
	}
}

// getAllTaskIDs 获取所有任务ID
func (s *URLSchedulerService) getAllTaskIDs(ctx context.Context) ([]string, error) {
	// 通过调用task-manager的API接口获取任务ID列表
	const pageSize = 100 // 每页获取的任务数量
	offset := 0
	allTaskIDs := make([]string, 0)

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	for {
		url := fmt.Sprintf("http://localhost:3500/api/v1/tasks?limit=%d&offset=%d", pageSize, offset)
		req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
		if err != nil {
			return nil, fmt.Errorf("创建请求失败: %w", err)
		}
		req.Header.Set("dapr-app-id", s.taskManagerAppID)

		resp, err := httpClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("调用taskmanager服务获取任务ID列表失败: %w", err)
		}

		// 检查响应状态
		if resp.StatusCode >= 400 {
			respBody, _ := io.ReadAll(resp.Body)
			resp.Body.Close()
			return nil, fmt.Errorf("调用taskmanager服务获取任务ID列表失败, 状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
		}

		respBody, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			return nil, fmt.Errorf("读取响应失败: %w", err)
		}

		var response struct {
			Success bool   `json:"success"`
			Error   string `json:"error"`
			Data    struct {
				Tasks []models.CrawlerTask `json:"tasks"`
				Total int                  `json:"total"`
			} `json:"data"`
		}

		if err := json.Unmarshal(respBody, &response); err != nil {
			return nil, fmt.Errorf("解析响应数据失败: %w", err)
		}

		if !response.Success {
			return nil, fmt.Errorf("获取任务ID列表失败: %s", response.Error)
		}

		// 提取任务ID
		for _, task := range response.Data.Tasks {
			allTaskIDs = append(allTaskIDs, task.ID)
		}

		// 判断是否已获取所有任务
		if len(response.Data.Tasks) < pageSize || len(allTaskIDs) >= response.Data.Total {
			break
		}

		// 更新偏移量，获取下一页
		offset += len(response.Data.Tasks)

		// 防止过度请求
		if offset > 1000 { // 设置一个合理的上限，避免无限循环
			log.Printf("警告: 任务数量超过1000，仅获取前1000个任务")
			break
		}
	}

	return allTaskIDs, nil
}

// getTask 获取任务详情
func (s *URLSchedulerService) getTask(ctx context.Context, taskID string) (*models.CrawlerTask, error) {
	url := fmt.Sprintf("http://localhost:3500/api/v1/tasks/%s", taskID)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("dapr-app-id", s.taskManagerAppID)

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("调用taskmanager服务获取任务详情失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode >= 400 {
		respBody, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("调用taskmanager服务获取任务详情失败, 状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var response struct {
		Success bool               `json:"success"`
		Data    models.CrawlerTask `json:"data"`
		Error   string             `json:"error"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("获取任务详情失败: %s", response.Error)
	}

	return &response.Data, nil
}

// reExecuteTask 重新执行周期性任务
func (s *URLSchedulerService) reExecuteTask(ctx context.Context, task *models.CrawlerTask) error {
	// 1. 将任务的InitialURLs添加到URL队列
	err := s.addURLsToQueue(ctx, task.ID, task.InitialURLs, task.Priority)
	if err != nil {
		return fmt.Errorf("添加URL到队列失败: %w", err)
	}

	// 3. 通过API更新任务的LastExecutedAt时间
	now := time.Now()
	// 构建请求体
	payload := map[string]interface{}{
		"last_executed_at": now.Format(time.RFC3339),
	}

	data, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	url := fmt.Sprintf("http://localhost:3500/api/v1/tasks/%s", task.ID)
	req, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("dapr-app-id", s.taskManagerAppID)

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("调用taskmanager服务更新任务失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode >= 400 {
		respBody, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("调用taskmanager服务更新任务失败, 状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	log.Printf("成功重新执行周期性任务 %s，添加了 %d 个URL到队列", task.ID, len(task.InitialURLs))
	return nil
}

// addURLsToQueue 添加URL到队列
func (s *URLSchedulerService) addURLsToQueue(ctx context.Context, taskID string, urls []models.ReqURL, priority models.TaskPriority) error {
	// 构建请求体
	payload := map[string]interface{}{
		"task_id":  taskID,
		"urls":     urls,
		"priority": priority,
	}

	data, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	url := "http://localhost:3500/api/v1/urls"
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("dapr-app-id", s.taskManagerAppID)

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("调用taskmanager服务添加URL失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode >= 400 {
		respBody, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("调用taskmanager服务添加URL失败, 状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	return nil
}

// scheduleURLs 获取待处理的URL并发布到Pubsub
func (s *URLSchedulerService) scheduleURLs(ctx context.Context) {
	// 获取待处理的URL
	urls, err := s.getPendingURLs(ctx, 5)
	if err != nil {
		log.Printf("获取待处理URL失败: %v", err)
		return
	}

	if len(urls) == 0 {
		return
	}

	// 发布URL到Pubsub
	for _, url := range urls {
		go s.publishURL(ctx, url)
	}
}

// getPendingURLs 获取待处理的URL
func (s *URLSchedulerService) getPendingURLs(ctx context.Context, limit int) ([]models.URLQueueItem, error) {
	url := fmt.Sprintf("http://localhost:3500/api/v1/urls/pending?limit=%d", limit)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("dapr-app-id", s.taskManagerAppID)

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("调用taskmanager服务失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var response struct {
		Success bool `json:"success"`
		Data    struct {
			URLs []models.URLQueueItem `json:"urls"`
		} `json:"data"`
		Error string `json:"error"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("获取待处理URL失败: %s", response.Error)
	}

	return response.Data.URLs, nil
}

// publishURL 将URL发布到Pubsub
func (s *URLSchedulerService) publishURL(ctx context.Context, urlItem models.URLQueueItem) {
	log.Printf("发布URL到Pubsub: ID=%d, URL=%s", urlItem.ID, urlItem.URL)

	// 更新状态为scheduled
	if err := s.updateURLStatus(ctx, urlItem.ID, models.URLStatusScheduled, ""); err != nil {
		log.Printf("更新URL状态为scheduled失败: %v", err)
		return
	}

	// 发布到Pubsub
	data, err := json.Marshal(urlItem)
	if err != nil {
		log.Printf("序列化URL数据失败: %v", err)
		s.updateURLStatus(ctx, urlItem.ID, models.URLStatusFailed, fmt.Sprintf("序列化失败: %v", err))
		return
	}

	if err := s.daprClient.PublishEvent(ctx, s.pubsubName, s.topicName, data); err != nil {
		log.Printf("发布URL到Pubsub失败: %v", err)
		s.updateURLStatus(ctx, urlItem.ID, models.URLStatusFailed, fmt.Sprintf("发布失败: %v", err))
		return
	}
}

// updateURLStatus 更新URL状态
func (s *URLSchedulerService) updateURLStatus(ctx context.Context, urlID int64, status models.URLStatus, errorMessage string) error {
	// 构建请求体
	payload := map[string]interface{}{
		"status": status,
	}
	if errorMessage != "" {
		payload["error_message"] = errorMessage
	}

	data, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	url := fmt.Sprintf("http://localhost:3500/api/v1/urls/%d/status", urlID)
	req, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("dapr-app-id", s.taskManagerAppID)

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("调用taskmanager服务更新URL状态失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode >= 400 {
		respBody, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("调用taskmanager服务更新URL状态失败, 状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	return nil
}

// Close 关闭服务
func (s *URLSchedulerService) Close() error {
	if s.daprClient != nil {
		s.daprClient.Close()
	}
	return nil
}
