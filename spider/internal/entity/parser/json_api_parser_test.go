package parser

import (
	"testing"
)

// TestFlattenJSONData 测试JSON数据扁平化功能
func TestFlattenJSONData(t *testing.T) {
	parser := &JSONAPIParser{}

	// 测试数据
	testData := map[string]interface{}{
		"id":   123,
		"name": "测试商品",
		"details": map[string]interface{}{
			"color": "红色",
			"size":  "大",
			"specs": map[string]interface{}{
				"weight": "1kg",
				"height": "10cm",
			},
		},
		"tags":  []interface{}{"电子", "热销"},
		"price": 99.99,
	}

	// 执行扁平化
	result := parser.flattenJSONData(testData)

	// 验证结果
	if result["id"] != 123 {
		t.<PERSON><PERSON><PERSON>("Expected id to be 123, got %v", result["id"])
	}
	if result["name"] != "测试商品" {
		t.<PERSON><PERSON>("Expected name to be '测试商品', got %v", result["name"])
	}
	if result["price"] != 99.99 {
		t.<PERSON><PERSON>rf("Expected price to be 99.99, got %v", result["price"])
	}
	if result["details_color"] != "红色" {
		t.<PERSON>("Expected details_color to be '红色', got %v", result["details_color"])
	}
	if result["details_size"] != "大" {
		t.Errorf("Expected details_size to be '大', got %v", result["details_size"])
	}

	// 验证嵌套对象被转为JSON字符串
	specsStr, ok := result["details_specs"].(string)
	if !ok {
		t.Errorf("Expected details_specs to be string, got %T", result["details_specs"])
	}
	if specsStr == "" {
		t.Error("Expected details_specs to contain JSON string")
	}

	// 验证数组被转为JSON字符串
	tagsStr, ok := result["tags"].(string)
	if !ok {
		t.Errorf("Expected tags to be string, got %T", result["tags"])
	}
	if tagsStr == "" {
		t.Error("Expected tags to contain JSON string")
	}
}
