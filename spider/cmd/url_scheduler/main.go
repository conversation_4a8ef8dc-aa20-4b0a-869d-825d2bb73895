package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"shovel/spider/internal/services"
)

func main() {
	log.Println("启动URL调度服务...")

	// 从环境变量获取配置，如果没有则使用默认值
	taskManagerAppID := getEnv("TASK_MANAGER_APP_ID", "task-manager")
	pubsubName := getEnv("PUBSUB_NAME", "pubsub")
	topicName := getEnv("TOPIC_NAME", "url-download")

	// 创建URL调度服务
	scheduler, err := services.NewURLSchedulerService(taskManagerAppID, pubsubName, topicName)
	if err != nil {
		log.Fatalf("创建URL调度服务失败: %v", err)
	}
	defer scheduler.Close()

	// 启动服务
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := scheduler.Start(ctx); err != nil {
		log.Fatalf("启动URL调度服务失败: %v", err)
	}

	log.Println("URL调度服务已启动，等待信号...")

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	<-sigChan
	log.Println("收到停止信号，正在关闭URL调度服务...")

	cancel()
	log.Println("URL调度服务已停止")
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
